#!/usr/bin/env python3
"""
Debug script to check hook names for LLaMA models.
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from itas.core.config import ModelConfig
from itas.core.model_loader import UniversalModelLoader

def debug_hook_names():
    """Debug hook names for LLaMA model."""
    
    print("🔍 Debugging hook names for LLaMA 3.1 8B...")
    
    # Create model config
    model_config = ModelConfig(
        model_name="meta-llama/Llama-3.1-8B-Instruct",
        use_flash_attention=True,
        torch_dtype="bfloat16",
        device_map={"": "cuda:0"},
        trust_remote_code=False,
    )
    
    # Load model
    model_loader = UniversalModelLoader(model_config)
    model, tokenizer = model_loader.load_model_and_tokenizer()
    
    # Get hook names
    hook_names = model_loader.get_hook_names()
    
    print("\n📋 Available hook names:")
    for hook_type, hook_pattern in hook_names.items():
        print(f"  {hook_type}: {hook_pattern}")
    
    print("\n🎯 Resolved hook names for layer 1:")
    for hook_type, hook_pattern in hook_names.items():
        resolved = hook_pattern.format(layer=1)
        print(f"  {hook_type}: {resolved}")
    
    print("\n🔍 Model structure inspection:")
    print(f"  Model class: {model.__class__.__name__}")
    print(f"  Has 'model' attr: {hasattr(model, 'model')}")
    print(f"  Has 'layers' attr: {hasattr(model, 'layers')}")
    
    if hasattr(model, 'model'):
        print(f"  model.model has 'layers': {hasattr(model.model, 'layers')}")
        if hasattr(model.model, 'layers'):
            print(f"  Number of layers: {len(model.model.layers)}")
            if len(model.model.layers) > 0:
                layer_0 = model.model.layers[0]
                print(f"  Layer 0 class: {layer_0.__class__.__name__}")
                print(f"  Layer 0 attributes: {[attr for attr in dir(layer_0) if not attr.startswith('_')]}")

if __name__ == "__main__":
    debug_hook_names()
