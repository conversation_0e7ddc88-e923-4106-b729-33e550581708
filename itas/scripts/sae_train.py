#!/usr/bin/env python3
"""
Scalable SAE Training Script with W&B Integration

This script automatically detects available GPUs and uses the optimal training strategy:
- Single GPU: Efficient single-GPU training
- Multiple GPUs: Distributed training with DataParallel

Features:
- Automatic GPU detection and scaling
- Weights & Biases integration for experiment tracking
- Support for various datasets (RedPajama, WikiText, etc.)
- Configurable SAE architectures (standard, gated, etc.)

Usage:
    python sae_train.py                    # Auto-detect and use all available GPUs with defaults
    CUDA_VISIBLE_DEVICES=0 python sae_train.py  # Force single GPU
    CUDA_VISIBLE_DEVICES=4,5,6,7 python sae_train.py  # Use specific GPUs

    # With custom parameters
    python sae_train.py --model_name "meta-llama/Llama-3.1-8B-Instruct" --hook_layer 16
    python sae_train.py --wandb_project "my-sae-experiments" --architecture "standard"
    python sae_train.py --no_wandb True --learning_rate 1e-4

    # Get help
    python sae_train.py -- --help
"""

import os
import torch
import logging
import fire
import wandb
from datetime import datetime

# ITAS imports
from itas import (
    SAEConfig,
    ModelConfig,
    DatasetConfig,
    TrainingConfig,
    UniversalModelLoader,
    DatasetManager,
    validate_config,
)
from itas.core.sae import TrainingSAE
from itas.core.activations_store import ActivationsStore

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def get_available_gpus():
    """Get list of available GPU device IDs."""
    if not torch.cuda.is_available():
        return []

    # Get CUDA_VISIBLE_DEVICES if set
    visible_devices = os.environ.get("CUDA_VISIBLE_DEVICES")
    if visible_devices:
        # Parse comma-separated device IDs
        try:
            device_ids = [
                int(d.strip()) for d in visible_devices.split(",") if d.strip()
            ]
            # Map to 0-indexed for torch
            return list(range(len(device_ids)))
        except ValueError:
            # Fallback if parsing fails
            return list(range(torch.cuda.device_count()))
    else:
        return list(range(torch.cuda.device_count()))


def detect_gpu_setup():
    """Detect and configure GPU setup."""
    # Respect CUDA_VISIBLE_DEVICES if already set (for single GPU assignment)
    # Only set defaults if not already configured
    if "CUDA_VISIBLE_DEVICES" not in os.environ:
        os.environ["CUDA_DEVICE_ORDER"] = "PCI_BUS_ID"
        # Check if we have multiple GPUs available and default to using them
        if torch.cuda.is_available() and torch.cuda.device_count() > 1:
            os.environ["CUDA_VISIBLE_DEVICES"] = "0,1,2,3"  # Default to GPUs 0-3
        else:
            os.environ["CUDA_VISIBLE_DEVICES"] = "0"  # Default to single GPU

    # Check GPU availability
    device = "cuda" if torch.cuda.is_available() else "cpu"
    num_gpus = torch.cuda.device_count()

    print(f"🔧 GPU Setup:")
    print(f"  Using device: {device}")
    print(f"  Available GPUs: {num_gpus}")
    print(
        f"  CUDA_VISIBLE_DEVICES: {os.environ.get('CUDA_VISIBLE_DEVICES', 'Not set')}"
    )

    if device == "cuda":
        for i in range(num_gpus):
            print(f"  GPU {i}: {torch.cuda.get_device_name(i)}")
            print(
                f"    VRAM: {torch.cuda.get_device_properties(i).total_memory / 1e9:.1f} GB"
            )

    return device, num_gpus


def train_sae(
    model_name: str = "meta-llama/Llama-3.1-8B-Instruct",
    dataset_name: str = "togethercomputer/RedPajama-Data-V2",
    architecture: str = "gated",
    expansion_factor: int = 32,
    hook_layer: int = 16,
    hook_point: str = "residual_post",
    hook_name: str = None,
    total_tokens: int = 1_000_000_000,  # 1B tokens for large-scale training
    batch_size: int = 8192,  # Larger batch size for better efficiency
    learning_rate: float = 1e-4,  # Lower LR for large-scale stable training
    l1_coefficient: float = 5e-4,  # Slightly lower for large-scale training
    output_dir: str = "./models/sae",
    no_wandb: bool = False,
    wandb_project: str = "scheming-sae-training",
    wandb_run_name: str = None,
):
    """
    Scalable SAE Training with Multi-GPU Support and W&B Integration

    This function automatically detects available GPUs and uses the optimal training strategy:
    - Single GPU: Efficient single-GPU training
    - Multiple GPUs: Distributed training with DataParallel

    Args:
        model_name: HuggingFace model name to train SAE on
        dataset_name: HuggingFace dataset name for training
        architecture: SAE architecture type (standard, gated, jumprelu)
        expansion_factor: SAE expansion factor (d_sae = d_in * expansion_factor)
        hook_layer: Target layer for SAE training (default: middle layer of model)
        hook_point: Hook point type for activation extraction (residual_post, residual_pre, attn_out, mlp_out)
        hook_name: Custom hook name pattern (overrides hook-point if provided). Use {layer} as placeholder.
        total_tokens: Total training tokens
        batch_size: Base batch size (will be scaled for multi-GPU)
        learning_rate: Learning rate for training
        l1_coefficient: L1 sparsity regularization coefficient
        output_dir: Output directory for checkpoints
        wandb_project: W&B project name
        wandb_run_name: W&B run name (auto-generated if not provided)
        no_wandb: Disable W&B logging
    """
    clean_model_name = model_name.split("/")[-1].lower()
    clean_dataset_name = dataset_name.split("/")[-1].lower()
    wandb_run_name = f"{clean_model_name}_{expansion_factor}x_layers.{hook_layer}.{hook_point}_{clean_dataset_name}_{architecture}"

    # Validate architecture choice
    valid_architectures = ["standard", "gated", "jumprelu"]
    if architecture not in valid_architectures:
        raise ValueError(
            f"Architecture must be one of {valid_architectures}, got {architecture}"
        )

    # Validate hook_point choice
    valid_hook_points = ["residual_post", "residual_pre", "attn_out", "mlp_out"]
    if hook_point not in valid_hook_points:
        raise ValueError(
            f"Hook point must be one of {valid_hook_points}, got {hook_point}"
        )

    # Create args object to maintain compatibility with existing code
    class Args:
        def __init__(self):
            self.model_name = model_name
            self.dataset_name = dataset_name
            self.architecture = architecture
            self.expansion_factor = expansion_factor
            self.hook_layer = hook_layer
            self.hook_point = hook_point
            self.hook_name = hook_name
            self.total_tokens = total_tokens
            self.batch_size = batch_size
            self.learning_rate = learning_rate
            self.l1_coefficient = l1_coefficient
            self.wandb_project = wandb_project
            self.wandb_run_name = wandb_run_name
            self.no_wandb = no_wandb

    args = Args()

    # Detect GPU setup
    device, num_gpus = detect_gpu_setup()

    if device == "cpu":
        print("❌ CUDA not available. This script requires GPU.")
        return

    # Get available GPU IDs
    gpu_ids = get_available_gpus()

    print(f"\n🚀 Launching scalable SAE training")
    print(f"  Available GPUs: {num_gpus}")
    print(f"  GPU IDs: {gpu_ids}")
    print(
        f"  Strategy: {'Multi-GPU (DataParallel)' if num_gpus > 1 else 'Single-GPU (Optimized)'}"
    )
    print(f"  Model: {args.model_name}")
    print(f"  Dataset: {args.dataset_name}")
    print(f"  Architecture: {args.architecture}")
    print(f"  Hook layer: {args.hook_layer or 'auto (middle layer)'}")
    print(f"  Hook point: {args.hook_point}")
    if args.hook_name:
        print(f"  Custom hook name: {args.hook_name}")
    print(f"  Expansion factor: {args.expansion_factor}")
    print(f"  Learning rate: {args.learning_rate}")
    print(f"  L1 coefficient: {args.l1_coefficient}")
    print(f"  W&B: {'Enabled' if not args.no_wandb else 'Disabled'}")

    # Create configuration
    config, args = create_config(num_gpus, args)

    # Validate configuration
    issues = validate_config(config)
    if issues:
        print(f"❌ Configuration issues: {issues}")
        return
    print("✓ Configuration validated")

    try:
        # Initialize trainer
        trainer = SAETrainer(config, gpu_ids, wandb_run_name=args.wandb_run_name)
        trainer.setup()

        # Train SAE
        sae = trainer.train()

        # Save model
        target_layer = config.hook_layer
        save_path = os.path.join(args.output_dir, wandb_run_name)
        torch.save(
            {
                "sae_state_dict": sae.state_dict(),
                "config": config.to_dict(),
            },
            save_path,
        )
        print(f"📁 SAE saved to: {save_path}")

        print(f"\n🎉 Training completed successfully!")
        print(f"  Model: {args.model_name}")
        print(f"  GPUs used: {len(gpu_ids)}")
        print(f"  Saved to: {save_path}")

    except Exception as e:
        logger.error(f"Training failed: {e}")
        raise


def create_config(num_gpus=1, args=None):
    """Create SAE configuration for single or multi-GPU training."""

    # For multi-GPU, use auto device mapping for optimal memory distribution
    # For single GPU, use explicit device to ensure proper placement
    if num_gpus > 1:
        device_map = "auto"
        print(f"🔧 Using device_map='auto' for {num_gpus} GPUs")
    else:
        # For single GPU, explicitly place on cuda:0 (which maps to the visible GPU)
        device_map = {"": "cuda:0"}
        print(f"🔧 Using single GPU device placement on cuda:0")

    model_config = ModelConfig(
        model_name=args.model_name,
        use_flash_attention=True,
        torch_dtype="bfloat16",
        device_map=device_map,
        trust_remote_code=False,
    )

    # Dataset configuration
    dataset_config = DatasetConfig(
        dataset_name=args.dataset_name,
        dataset_split="train",
        text_column=(
            "raw_content" if "RedPajama-Data-V2" in args.dataset_name else "text"
        ),
        max_seq_length=16384,
        chunk_size=4096,
        streaming=True,  # Enable streaming for large datasets like RedPajama-V2
        num_proc=min(8, os.cpu_count() // 2),  # Reduce CPU usage for multi-GPU
        trust_remote_code=True,
        # RedPajama-V2 specific configuration for English-only 10B tokens
        redpajama_languages=(
            ["en"] if "RedPajama-Data-V2" in args.dataset_name else None
        ),
        redpajama_snapshots=(
            ["2023-06", "2022-49"] if "RedPajama-Data-V2" in args.dataset_name else None
        ),
        redpajama_partition=(
            "head_middle" if "RedPajama-Data-V2" in args.dataset_name else "head_middle"
        ),
        max_tokens=(
            10_000_000_000 if "RedPajama-Data-V2" in args.dataset_name else None
        ),  # 10B tokens
        token_count_column=(
            "raw_content" if "RedPajama-Data-V2" in args.dataset_name else "text"
        ),
    )

    # Training configuration - only scale batch size for true multi-GPU setups
    base_batch_size = args.batch_size
    if num_gpus > 1:
        # Scale batch size for multi-GPU training
        total_batch_size = base_batch_size * max(1, num_gpus // 2)
        print(
            f"🔧 Scaling batch size for {num_gpus} GPUs: {base_batch_size} -> {total_batch_size}"
        )
    else:
        # Keep original batch size for single GPU
        total_batch_size = base_batch_size
        print(f"🔧 Using single GPU batch size: {total_batch_size}")

    # Generate run name if not provided
    run_name = args.wandb_run_name
    if run_name is None and not args.no_wandb:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_short = args.model_name.split("/")[-1].lower()
        layer_str = str(args.hook_layer) if args.hook_layer else "auto"
        hook_point_str = args.hook_point if args.hook_point != "mlp_out" else "mlp"
        run_name = f"{model_short}_layer{layer_str}_{hook_point_str}_{args.architecture}_{timestamp}"

    training_config = TrainingConfig(
        total_training_tokens=args.total_tokens,
        batch_size=total_batch_size,
        learning_rate=args.learning_rate,
        l1_coefficient=args.l1_coefficient,
        lr_scheduler="cosine",
        lr_warm_up_steps=max(
            1000, args.total_tokens // 200_000
        ),  # Scale warmup with dataset size
        checkpoint_every_n_tokens=max(
            10_000_000, args.total_tokens // 20
        ),  # Checkpoint every 5% of training
        save_checkpoint_dir="./checkpoints",
        log_every_n_steps=50,  # More frequent logging for large-scale training
        eval_every_n_tokens=max(
            5_000_000, args.total_tokens // 40
        ),  # Eval every 2.5% of training
        use_wandb=not args.no_wandb,
        wandb_project=args.wandb_project,
    )

    # Determine hook configuration
    # If custom hook name is provided, use it directly
    if args.hook_name:
        hook_name = args.hook_name
    else:
        # Use hook_point to determine the hook name pattern
        # This will be resolved with actual hook names after model loading
        hook_name = f"hook_point:{args.hook_point}"

    # SAE configuration
    config = SAEConfig(
        model=model_config,
        dataset=dataset_config,
        training=training_config,
        architecture=args.architecture,
        expansion_factor=args.expansion_factor,
        hook_layer=args.hook_layer,  # Will be set to middle layer if None
        hook_name=hook_name,  # Will be resolved after model loading
        activation_fn="relu",
        normalize_decoder=True,
        device="cuda:0",  # Primary device (maps to CUDA_VISIBLE_DEVICES[0])
        dtype="float32",
        seed=42,
    )

    return config, args


class SAETrainer:
    """Scalable SAE trainer that works with single or multiple GPUs using DataParallel."""

    def __init__(self, config, gpu_ids=None, wandb_run_name=None):
        self.config = config
        self.gpu_ids = gpu_ids or [0]
        self.num_gpus = len(self.gpu_ids)
        self.device = f"cuda:{self.gpu_ids[0]}" if torch.cuda.is_available() else "cpu"
        self.wandb_run_name = wandb_run_name

        # Initialize components
        self.model = None
        self.tokenizer = None
        self.sae = None
        self.optimizer = None
        self.dataset_manager = None

    def setup(self):
        """Setup model, SAE, and data components."""
        print(
            f"🔧 Setting up {'multi-GPU' if self.num_gpus > 1 else 'single-GPU'} SAE training..."
        )
        print(f"  Using GPUs: {self.gpu_ids}")

        # Load model and tokenizer
        model_loader = UniversalModelLoader(self.config.model)
        self.model, self.tokenizer = model_loader.load_model_and_tokenizer()

        # For multi-GPU with device_map="auto", don't move the model to a single device
        # The device_map="auto" already handles optimal device placement
        # Moving it to a single device would undo the automatic distribution
        if self.num_gpus > 1:
            print(f"🔧 Model loaded with device_map='auto' across {self.num_gpus} GPUs")
            print(
                f"   Model will use automatic device placement for optimal memory usage"
            )
        else:
            # For single GPU, ensure model is on the correct device
            self.model = self.model.to(self.device)

        # Get model info and update config
        model_info = model_loader.get_model_info()
        hook_names = model_loader.get_hook_names()

        # Set target layer if not specified
        target_layer = self.config.hook_layer

        # Resolve hook name if using hook_point syntax
        if self.config.hook_name.startswith("hook_point:"):
            hook_point = self.config.hook_name.split(":", 1)[1]

            print(f"🔍 Resolving hook point: '{hook_point}'")
            print(f"🔍 Available hook names: {list(hook_names.keys())}")

            # The hook_point names should match the keys in hook_names dictionary
            if hook_point in hook_names:
                hook_pattern = hook_names[hook_point]
                self.config.hook_name = hook_pattern.format(layer=target_layer)
                print(
                    f"✅ Successfully resolved hook: '{hook_point}' -> '{self.config.hook_name}'"
                )
            else:
                # Fallback to a reasonable default
                print(
                    f"⚠️  Hook point '{hook_point}' not found in {list(hook_names.keys())}, using mlp_out as fallback"
                )
                if "mlp_out" in hook_names:
                    hook_pattern = hook_names["mlp_out"]
                    self.config.hook_name = hook_pattern.format(layer=target_layer)
                    print(f"🔄 Using fallback hook: '{self.config.hook_name}'")
                else:
                    # Last resort fallback
                    self.config.hook_name = f"model.layers.{target_layer}.mlp"
                    print(f"🔄 Using last resort hook: '{self.config.hook_name}'")

        print(f"✓ Model loaded: {model_info['model_name']}")
        print(f"  Architecture: {model_info['architecture']}")
        print(f"  Hidden size: {model_info['hidden_size']}")
        print(f"  Layers: {model_info['num_layers']}")
        print(f"  Target layer: {target_layer}")
        print(f"  Hook name: {self.config.hook_name}")

        # Print available hook points for reference
        print(f"  Available hook points: {list(hook_names.keys())}")

        # Setup dataset
        self.dataset_manager = DatasetManager(self.config.dataset, self.tokenizer)
        self.dataset_manager.load_dataset()
        self.dataset_manager.preprocess_dataset()

        dataset_info = self.dataset_manager.get_dataset_info()
        print(f"✓ Dataset loaded: {dataset_info['processed_size']:,} samples")

        # Initialize SAE
        d_in = model_info["hidden_size"]

        self.sae = TrainingSAE(
            d_in=d_in,
            d_sae=d_in * self.config.expansion_factor,
            architecture=self.config.architecture,
            activation_fn=self.config.activation_fn,
            normalize_decoder=self.config.normalize_decoder,
            device=self.device,
            dtype=getattr(torch, self.config.dtype),
        )

        # For multi-GPU, we'll manually handle parallelization to avoid DataParallel issues
        # with custom output objects. Keep SAE on primary device.
        if self.num_gpus > 1:
            print(f"🔥 Multi-GPU training enabled across {self.num_gpus} GPUs")
            print(f"   SAE will be manually distributed across GPUs during training")
            # Note: Keep model unwrapped for activation hooks to work properly

        # Initialize optimizer
        self.optimizer = torch.optim.Adam(
            self.sae.parameters(),
            lr=self.config.training.learning_rate,
            betas=(0.9, 0.999),
            weight_decay=0.0,
        )

        print(f"✓ SAE initialized")
        print(f"  d_in: {d_in}")
        print(f"  d_sae: {d_in * self.config.expansion_factor}")
        print(f"  Architecture: {self.config.architecture}")
        print(f"  Multi-GPU: {self.num_gpus > 1}")
        print(f"  Effective batch size: {self.config.training.batch_size}")

        # Initialize W&B if enabled
        if self.config.training.use_wandb:
            # Generate run name if not provided
            run_name = getattr(self, "wandb_run_name", None)
            if run_name is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                model_short = self.config.model.model_name.split("/")[-1].lower()
                layer_str = str(self.config.hook_layer)
                hook_point_str = "mlp"  # Default abbreviation
                run_name = f"{model_short}_layer{layer_str}_{hook_point_str}_{self.config.architecture}_{timestamp}"

            wandb.init(
                project=self.config.training.wandb_project,
                name=run_name,
                config={
                    "model_name": self.config.model.model_name,
                    "dataset_name": self.config.dataset.dataset_name,
                    "architecture": self.config.architecture,
                    "expansion_factor": self.config.expansion_factor,
                    "hook_layer": self.config.hook_layer,
                    "hook_name": self.config.hook_name,
                    "d_in": d_in,
                    "d_sae": d_in * self.config.expansion_factor,
                    "total_training_tokens": self.config.training.total_training_tokens,
                    "batch_size": self.config.training.batch_size,
                    "learning_rate": self.config.training.learning_rate,
                    "l1_coefficient": self.config.training.l1_coefficient,
                    "num_gpus": self.num_gpus,
                    "gpu_ids": self.gpu_ids,
                },
            )
            print(f"✓ W&B initialized: {self.config.training.wandb_project}/{run_name}")

    def train(self):
        """Train the SAE."""
        print("🏋️ Starting SAE training...")

        # Setup activations store
        activations_store = ActivationsStore(
            self.model,
            self.tokenizer,
            self.config,
            self.dataset_manager,
        )

        # Training parameters
        total_tokens = self.config.training.total_training_tokens
        batch_size = self.config.training.batch_size
        total_steps = total_tokens // batch_size

        print(f"  Total tokens: {total_tokens:,}")
        print(f"  Batch size: {batch_size}")
        print(f"  Total steps: {total_steps:,}")
        print(f"  GPUs: {self.num_gpus}")

        # Estimate training time
        tokens_per_second_estimate = 1000 * self.num_gpus  # Scale with GPUs
        estimated_hours = total_tokens / tokens_per_second_estimate / 3600
        print(f"  Estimated time: {estimated_hours:.1f} hours")

        # Training loop
        step = 0
        tokens_processed = 0

        try:
            with activations_store:
                activations_store.start_streaming(batch_size=32)

                # Add initial delay to let streaming start
                import time

                time.sleep(2.0)
                print("🔄 Waiting for activation streaming to initialize...")

                consecutive_failures = 0
                max_consecutive_failures = 50

                while tokens_processed < total_tokens and step < total_steps:
                    # Get batch of activations with timeout
                    activations = activations_store.get_next_batch(timeout=5.0)
                    if activations is None:
                        consecutive_failures += 1
                        if consecutive_failures > max_consecutive_failures:
                            print(
                                f"❌ Too many consecutive failures ({consecutive_failures}). Restarting streaming..."
                            )
                            activations_store.stop_streaming()
                            time.sleep(1.0)
                            activations_store.start_streaming(batch_size=32)
                            consecutive_failures = 0
                        continue

                    consecutive_failures = 0

                    # Move to device and prepare
                    activations = activations.to(self.device).float()
                    if len(activations.shape) > 2:
                        activations = activations.view(-1, activations.shape[-1])

                    # Limit batch size
                    if activations.shape[0] > batch_size:
                        activations = activations[:batch_size]

                    # Training step
                    self.optimizer.zero_grad()

                    # Forward pass - use training_forward for both single and multi-GPU
                    # For multi-GPU, the benefit comes from parallel activation collection,
                    # not from parallelizing the SAE forward pass
                    output = self.sae.training_forward(
                        activations,
                        l1_coefficient=self.config.training.l1_coefficient,
                    )

                    # Compute total loss
                    total_loss = output.mse_loss + output.l1_loss
                    if output.aux_loss is not None:
                        total_loss = total_loss + output.aux_loss

                    # Backward pass
                    total_loss.backward()

                    # Gradient clipping
                    torch.nn.utils.clip_grad_norm_(self.sae.parameters(), max_norm=1.0)

                    # Optimizer step
                    self.optimizer.step()

                    # Update counters
                    step += 1
                    tokens_processed += activations.shape[0]

                    # Logging
                    if step % self.config.training.log_every_n_steps == 0:
                        metrics = {
                            "step": step,
                            "tokens_processed": tokens_processed,
                            "total_loss": total_loss.item(),
                            "mse_loss": output.mse_loss.item(),
                            "l1_loss": output.l1_loss.item(),
                            "progress": tokens_processed / total_tokens,
                        }

                        # Add auxiliary loss if available
                        if hasattr(output, "aux_loss") and output.aux_loss is not None:
                            metrics["aux_loss"] = output.aux_loss.item()

                        # Console logging
                        print(
                            f"Step {step:,}/{total_steps:,} | "
                            f"Tokens: {tokens_processed:,}/{total_tokens:,} | "
                            f"Loss: {total_loss.item():.6f} | "
                            f"MSE: {output.mse_loss.item():.6f} | "
                            f"L1: {output.l1_loss.item():.6f}"
                        )

                        # W&B logging
                        if self.config.training.use_wandb:
                            wandb.log(metrics)

                activations_store.stop_streaming()

        except Exception as e:
            logger.error(f"Training failed: {e}")
            raise

        print("✅ Training completed!")

        # Finish W&B run
        if self.config.training.use_wandb:
            wandb.finish()
            print("✓ W&B run finished")

        # Return the SAE (no longer wrapped with DataParallel)
        return self.sae


def main():
    """Main function to launch single or multi-GPU training using Fire."""
    fire.Fire(train_sae)


if __name__ == "__main__":
    main()
