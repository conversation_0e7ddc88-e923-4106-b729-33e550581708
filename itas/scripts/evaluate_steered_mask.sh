# Define the list of input CSV files
input_files=(
    "continuations_Llama-3.1-8B-Instruct.csv"
    "disinformation_Llama-3.1-8B-Instruct.csv"
    "doubling_down_known_facts_Llama-3.1-8B-Instruct.csv"
    "known_facts_Llama-3.1-8B-Instruct.csv"
    "provided_facts_Llama-3.1-8B-Instruct.csv"
    "statistics_Llama-3.1-8B-Instruct.csv"
)

# Define available GPUs (modify this list as needed)
gpus=(0 1 2 3 4 5)

# Common parameters
config_path="/data_x/junkim100/projects/scheming_sae/itas/results/steering_config_eleuther_l23_x64.json"
activations_path="/data_x/junkim100/projects/scheming_sae/itas/results/functional_activations_eleuther_l23_x64.pt"
output_dir="/data_x/junkim100/projects/scheming_sae/mask_benchmark/mask/csv_data/responses"
steering_direction="truthful" # either "truthful" or "scheming"
steering_alpha=1.5
input_dir="/data_x/junkim100/projects/scheming_sae/mask_benchmark/mask/csv_data/original/llama-3.1-8b_responses"

# Loop through the files and assign GPUs
for i in "${!input_files[@]}"; do
    gpu_idx=$((i % ${#gpus[@]}))  # Use modulo to cycle through available GPUs
    gpu_id=${gpus[$gpu_idx]}      # Get the actual GPU ID from the array
    input_file="${input_files[$i]}"

    echo "Processing ${input_file} on GPU ${gpu_id}"
    CUDA_VISIBLE_DEVICES=$gpu_id python mask_response_generator.py \
        --input_csv="$input_dir/$input_file" \
        --config_path="$config_path" \
        --activations_path="$activations_path" \
        --steering_direction="$steering_direction" \
        --output_dir="$output_dir" \
        --steering_alpha=$steering_alpha \
        &
done

# Wait for all background processes to complete
wait
