# RedPajama-V2 English Filtering for SAE Training

This document describes the new filtering logic added to support training SAEs on approximately 10B tokens of English data from RedPajama-Data-V2.

## Overview

The filtering system automatically:
- Selects only English language data (`languages=["en"]`)
- Uses high-quality snapshots (`snapshots=["2023-06", "2022-49"]`)
- Limits data to approximately 10B tokens
- Enables streaming for memory-efficient processing
- Uses the `head_middle` partition for better quality

## Changes Made

### 1. Enhanced DatasetConfig (`itas/itas/core/config.py`)

Added new configuration parameters:

```python
# RedPajama-V2 specific filtering
redpajama_languages: Optional[List[str]] = None
redpajama_snapshots: Optional[List[str]] = None  
redpajama_partition: str = "head_middle"

# Token limiting
max_tokens: Optional[int] = None
token_count_column: str = "raw_content"
```

### 2. Enhanced DatasetManager (`itas/itas/core/dataset_manager.py`)

Added methods:
- `_prepare_redpajama_v2_kwargs()` - Configures RedPajama-V2 loading parameters
- `_apply_filters()` - Applies token limits to non-streaming datasets
- `_apply_streaming_filters()` - Applies token limits to streaming datasets
- `_chunk_and_tokenize_streaming()` - Handles streaming tokenization and chunking

### 3. Updated SAE Training Script (`itas/scripts/sae_train.py`)

Automatically detects RedPajama-V2 and applies appropriate configuration:

```python
# RedPajama-V2 specific configuration for English-only 10B tokens
redpajama_languages=["en"] if "RedPajama-Data-V2" in args.dataset_name else None,
redpajama_snapshots=["2023-06", "2022-49"] if "RedPajama-Data-V2" in args.dataset_name else None,
max_tokens=10_000_000_000 if "RedPajama-Data-V2" in args.dataset_name else None,  # 10B tokens
```

## Usage

### Basic SAE Training with RedPajama-V2

```bash
python itas/scripts/sae_train.py \
    --dataset_name "togethercomputer/RedPajama-Data-V2" \
    --hook_layer 16 \
    --total_tokens 50000000 \
    --batch_size 4096
```

The script will automatically:
- Enable streaming mode
- Filter for English language only
- Use recent high-quality snapshots
- Limit to 10B tokens total
- Use `raw_content` as the text column

### Manual Configuration

```python
from itas.core.config import DatasetConfig
from itas.core.dataset_manager import DatasetManager

# Configure for RedPajama-V2 English filtering
dataset_config = DatasetConfig(
    dataset_name="togethercomputer/RedPajama-Data-V2",
    streaming=True,  # Essential for large datasets
    text_column="raw_content",
    redpajama_languages=["en"],  # English only
    redpajama_snapshots=["2023-06", "2022-49"],  # Recent snapshots
    redpajama_partition="head_middle",  # High-quality partition
    max_tokens=10_000_000_000,  # 10B token limit
    trust_remote_code=True,
)

# Create dataset manager and load data
dataset_manager = DatasetManager(dataset_config, tokenizer)
dataset = dataset_manager.load_dataset()
processed_dataset = dataset_manager.preprocess_dataset()
```

### Testing the Configuration

Run the test script to verify the filtering works:

```bash
python itas/scripts/test_redpajama_filtering.py
```

## Technical Details

### Token Estimation

The system uses a rough approximation of 1 token ≈ 4 characters for efficient token counting without full tokenization. This provides a reasonable estimate for filtering purposes.

### Streaming Support

- Uses `IterableDataset` for memory-efficient processing
- Implements streaming tokenization and chunking
- Maintains a token buffer for efficient chunk creation
- Automatically stops when token limit is reached

### Quality Filtering

- Uses `head_middle` partition (higher quality than `tail`)
- Selects recent snapshots (2023-06, 2022-49) for better data quality
- Filters for English language only to ensure consistency

### Memory Efficiency

- Streaming mode prevents loading entire dataset into memory
- Processes data in chunks to maintain constant memory usage
- Suitable for training on large datasets with limited RAM

## Performance Considerations

1. **Streaming is Essential**: For 10B tokens, streaming mode is required to avoid memory issues
2. **Token Estimation**: Character-based estimation is fast but approximate
3. **Snapshot Selection**: Using fewer snapshots reduces download time
4. **Chunk Size**: 4096 tokens provides good balance of efficiency and memory usage

## Troubleshooting

### Common Issues

1. **Memory Errors**: Ensure streaming=True for large datasets
2. **Slow Loading**: Reduce number of snapshots or use cached data
3. **Token Count Mismatch**: Character-based estimation is approximate

### Debugging

Enable detailed logging:

```python
import logging
logging.basicConfig(level=logging.INFO)
```

Check dataset info:

```python
info = dataset_manager.get_dataset_info()
print(info)
```

## Future Improvements

1. **Exact Token Counting**: Use actual tokenizer for precise counts
2. **Quality Signals**: Integrate RedPajama-V2 quality signals for better filtering
3. **Caching**: Add preprocessing cache for repeated experiments
4. **Progress Tracking**: Add progress bars for long-running operations
