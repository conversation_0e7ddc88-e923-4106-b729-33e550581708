# Optimized Default Values for Large-Scale SAE Training

This document explains the updated default values optimized for training SAEs on large datasets like RedPajama-V2 (10B tokens).

## Summary of Changes

### 🔧 **Training Parameters**

| Parameter | Old Default | New Default | Reasoning |
|-----------|-------------|-------------|-----------|
| `total_tokens` | 50M | 1B | Better utilization of 10B token dataset |
| `batch_size` | 4096 | 8192 | Improved efficiency and GPU utilization |
| `learning_rate` | 3e-4 | 1e-4 | More stable training for large-scale |
| `l1_coefficient` | 1e-3 | 5e-4 | Better sparsity balance for large datasets |

### 📊 **Training Configuration Defaults**

| Parameter | Old Default | New Default | Reasoning |
|-----------|-------------|-------------|-----------|
| `total_training_tokens` | 2M | 100M | More realistic for meaningful SAE training |
| `batch_size` | 4096 | 8192 | Better throughput and stability |
| `learning_rate` | 3e-4 | 1e-4 | Stable convergence for large-scale training |
| `lr_scheduler` | "constant" | "cosine" | Better learning dynamics |
| `lr_warm_up_steps` | 0 | 5000 | Stable training start |
| `l1_warm_up_steps` | 0 | 2000 | Gradual sparsity introduction |

### 🔄 **Checkpointing & Logging**

| Parameter | Old Default | New Default | Reasoning |
|-----------|-------------|-------------|-----------|
| `checkpoint_every_n_tokens` | 100K | 50M | Reasonable frequency for long training |
| `log_every_n_steps` | 100 | 50 | More frequent monitoring |
| `eval_every_n_tokens` | 100K | 25M | Balanced evaluation frequency |

## Detailed Rationale

### 🎯 **Total Tokens: 50M → 1B**

**Why**: With 10B tokens available, training on only 50M (0.5%) severely underutilizes the dataset. 1B tokens (10%) provides:
- Better feature learning from diverse data
- More robust SAE representations
- Improved generalization capabilities

**Impact**: ~20x longer training time, but significantly better SAE quality.

### 📈 **Batch Size: 4096 → 8192**

**Why**: Larger batch sizes for large-scale training provide:
- Better gradient estimates
- Improved training stability
- More efficient GPU utilization
- Reduced training variance

**Impact**: 2x memory usage per GPU, but better convergence and efficiency.

### 🎛️ **Learning Rate: 3e-4 → 1e-4**

**Why**: Lower learning rate for large-scale training:
- Prevents instability with large batches
- Better convergence with more data
- Reduces risk of overshooting optima
- Standard practice for large-scale training

**Impact**: Slower initial progress, but more stable final convergence.

### 🎯 **L1 Coefficient: 1e-3 → 5e-4**

**Why**: Slightly reduced sparsity pressure:
- Large datasets provide more signal for feature learning
- Prevents over-sparsification with abundant data
- Better balance between reconstruction and sparsity
- Allows more nuanced feature representations

**Impact**: Slightly denser activations, but better feature quality.

### 📅 **Checkpointing: 100K → 50M tokens**

**Why**: More reasonable frequency for long training:
- Reduces I/O overhead during training
- Still provides recovery points every ~5% of training
- Balances safety with efficiency
- Appropriate for multi-day training runs

**Impact**: Fewer checkpoint files, reduced storage overhead.

## Adaptive Scaling

The training script now includes **adaptive scaling** based on total tokens:

```python
# Scale warmup with dataset size
lr_warm_up_steps=max(1000, args.total_tokens // 200_000)

# Checkpoint every 5% of training
checkpoint_every_n_tokens=max(10_000_000, args.total_tokens // 20)

# Eval every 2.5% of training  
eval_every_n_tokens=max(5_000_000, args.total_tokens // 40)
```

This ensures appropriate scaling for different training sizes.

## Performance Estimates

### 🕐 **Training Time Estimates**

With the new defaults on 4 GPUs:

| Configuration | Estimated Time | Tokens/GPU |
|---------------|----------------|------------|
| 1B tokens, 8192 batch | ~24-48 hours | 250M |
| 500M tokens, 8192 batch | ~12-24 hours | 125M |
| 100M tokens, 8192 batch | ~2-5 hours | 25M |

### 💾 **Memory Requirements**

| Batch Size | GPU Memory (LLaMA 8B) | Recommended GPU |
|------------|----------------------|-----------------|
| 8192 | ~20-24 GB | RTX 4090, A100 |
| 4096 | ~12-16 GB | RTX 3090, RTX 4080 |
| 2048 | ~8-12 GB | RTX 3080, RTX 4070 |

## Usage Examples

### 🚀 **Quick Training (Testing)**
```bash
python sae_train.py --total_tokens 10000000 --batch_size 4096
```

### 🎯 **Standard Training (Recommended)**
```bash
python sae_train.py --total_tokens 1000000000 --batch_size 8192
```

### 🔥 **Large-Scale Training (Full Dataset)**
```bash
python sae_train.py --total_tokens 5000000000 --batch_size 8192
```

### 🔧 **Custom Configuration**
```bash
python sae_train.py \
    --total_tokens 2000000000 \
    --batch_size 8192 \
    --learning_rate 5e-5 \
    --l1_coefficient 3e-4
```

## Monitoring Recommendations

### 📊 **Key Metrics to Watch**

1. **Loss Convergence**: Should decrease steadily
2. **Sparsity**: Target 90-95% zero activations
3. **Reconstruction Error**: Should stabilize
4. **Dead Features**: Should be < 10%
5. **GPU Utilization**: Should be > 90%

### 🔍 **Early Stopping Criteria**

- Loss plateau for > 25M tokens
- Sparsity outside 85-98% range
- Reconstruction error increasing
- > 20% dead features

## Troubleshooting

### 🚨 **Common Issues**

1. **OOM Errors**: Reduce batch_size to 4096 or 2048
2. **Slow Training**: Increase batch_size if memory allows
3. **Poor Convergence**: Reduce learning_rate to 5e-5
4. **Over-sparsification**: Reduce l1_coefficient to 3e-4

### 🔧 **Performance Tuning**

1. **Multi-GPU**: Use train_all_layers_sae.sh for parallel training
2. **Memory**: Enable gradient checkpointing for larger batches
3. **Speed**: Use mixed precision (bfloat16) for faster training
4. **Storage**: Use fast SSD for dataset caching

These optimized defaults provide a good starting point for large-scale SAE training while maintaining flexibility for different use cases and hardware configurations.
