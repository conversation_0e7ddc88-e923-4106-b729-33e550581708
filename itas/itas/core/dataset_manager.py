"""
Universal dataset manager for HuggingFace datasets.

This module provides flexible dataset loading, preprocessing, and management
for SAE training with any HuggingFace dataset.
"""

import logging
from typing import Optional, Dict, Any, Union, Iterator
from pathlib import Path
import torch
from torch.utils.data import DataLoader, Dataset as TorchDataset
from datasets import Dataset, load_dataset, DatasetDict
from transformers import PreTrainedTokenizer
import numpy as np
from multiprocessing import cpu_count

from .config import DatasetConfig

logger = logging.getLogger(__name__)


class DatasetManager:
    """
    Universal manager for HuggingFace datasets with preprocessing capabilities.

    Handles loading, tokenization, chunking, and batching of datasets
    for SAE training and evaluation.
    """

    def __init__(self, config: DatasetConfig, tokenizer: PreTrainedTokenizer):
        """
        Initialize the dataset manager.

        Args:
            config: Dataset configuration
            tokenizer: Tokenizer for text processing
        """
        self.config = config
        self.tokenizer = tokenizer
        self._dataset = None
        self._processed_dataset = None

    def load_dataset(self) -> Dataset:
        """
        Load dataset from HuggingFace or local path.

        Returns:
            Loaded dataset

        Raises:
            ValueError: If dataset loading fails
        """
        try:
            logger.info(f"Loading dataset: {self.config.dataset_name}")

            # Handle different dataset sources
            if Path(self.config.dataset_name).exists():
                # Local dataset
                self._dataset = Dataset.load_from_disk(
                    self.config.dataset_name, keep_in_memory=not self.config.streaming
                )
            else:
                # HuggingFace dataset
                self._dataset = load_dataset(
                    self.config.dataset_name,
                    split=self.config.dataset_split,
                    revision=self.config.dataset_revision,
                    streaming=self.config.streaming,
                    trust_remote_code=self.config.trust_remote_code,
                    cache_dir=self.config.cache_dir,
                    **self.config.dataset_kwargs,
                )

            logger.info(
                f"Successfully loaded dataset with {len(self._dataset)} examples"
            )
            return self._dataset

        except Exception as e:
            logger.error(f"Failed to load dataset {self.config.dataset_name}: {str(e)}")
            raise ValueError(f"Dataset loading failed: {str(e)}") from e

    def preprocess_dataset(self, dataset: Optional[Dataset] = None) -> Dataset:
        """
        Preprocess dataset with tokenization and chunking.

        Args:
            dataset: Dataset to preprocess (uses loaded dataset if None)

        Returns:
            Preprocessed dataset
        """
        if dataset is None:
            if self._dataset is None:
                raise ValueError("No dataset loaded. Call load_dataset() first.")
            dataset = self._dataset

        logger.info("Preprocessing dataset...")

        # Tokenize and chunk the dataset
        processed = self._chunk_and_tokenize(dataset)

        # Cache the processed dataset
        self._processed_dataset = processed

        logger.info(f"Preprocessing complete. Dataset size: {len(processed)}")
        return processed

    def _chunk_and_tokenize(self, dataset: Dataset) -> Dataset:
        """
        Perform GPT-style chunking and tokenization.

        Args:
            dataset: Input dataset

        Returns:
            Chunked and tokenized dataset
        """

        def tokenize_function(examples):
            """Tokenize a batch of examples."""
            # Extract text from the specified column
            texts = examples[self.config.text_column]

            # Tokenize all texts in the batch
            tokenized = self.tokenizer(
                texts,
                truncation=False,
                padding=False,
                return_attention_mask=False,
                return_token_type_ids=False,
            )

            return tokenized

        def chunk_function(examples):
            """Chunk tokenized sequences into fixed-length segments."""
            input_ids = examples["input_ids"]

            # Flatten all input_ids and add EOS tokens between sequences
            all_tokens = []
            eos_token_id = self.tokenizer.eos_token_id or self.tokenizer.pad_token_id

            for ids in input_ids:
                all_tokens.extend(ids)
                all_tokens.append(eos_token_id)

            # Create chunks of specified length
            chunks = []
            chunk_size = self.config.chunk_size
            overlap = self.config.overlap_size

            for i in range(0, len(all_tokens) - chunk_size + 1, chunk_size - overlap):
                chunk = all_tokens[i : i + chunk_size]
                if len(chunk) == chunk_size:
                    chunks.append(chunk)

            return {"input_ids": chunks}

        # Apply tokenization
        tokenized_dataset = dataset.map(
            tokenize_function,
            batched=True,
            batch_size=1000,
            num_proc=self.config.num_proc or cpu_count() // 2,
            remove_columns=dataset.column_names,
            load_from_cache_file=self.config.load_from_cache_file,
            desc="Tokenizing",
        )

        # Apply chunking
        chunked_dataset = tokenized_dataset.map(
            chunk_function,
            batched=True,
            batch_size=1000,
            num_proc=self.config.num_proc or cpu_count() // 2,
            remove_columns=tokenized_dataset.column_names,
            load_from_cache_file=self.config.load_from_cache_file,
            desc="Chunking",
        )

        # Flatten the chunks
        def flatten_chunks(examples):
            """Flatten nested chunks into individual examples."""
            flattened_input_ids = []
            for chunk_list in examples["input_ids"]:
                flattened_input_ids.extend(chunk_list)
            return {"input_ids": flattened_input_ids}

        final_dataset = chunked_dataset.map(
            flatten_chunks,
            batched=True,
            batch_size=1000,
            remove_columns=chunked_dataset.column_names,
            load_from_cache_file=self.config.load_from_cache_file,
            desc="Flattening",
        )

        # Set format for PyTorch
        final_dataset.set_format(type="torch", columns=["input_ids"])

        return final_dataset

    def get_dataloader(
        self,
        batch_size: int = 32,
        shuffle: bool = True,
        num_workers: int = 0,
        dataset: Optional[Dataset] = None,
    ) -> DataLoader:
        """
        Create a PyTorch DataLoader for the dataset.

        Args:
            batch_size: Batch size for the DataLoader
            shuffle: Whether to shuffle the dataset
            num_workers: Number of worker processes
            dataset: Dataset to use (uses processed dataset if None)

        Returns:
            PyTorch DataLoader
        """
        if dataset is None:
            if self._processed_dataset is None:
                raise ValueError(
                    "No processed dataset available. Call preprocess_dataset() first."
                )
            dataset = self._processed_dataset

        return DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=shuffle,
            num_workers=num_workers,
            pin_memory=torch.cuda.is_available(),
            drop_last=True,  # Ensure consistent batch sizes
        )

    def get_streaming_iterator(
        self, batch_size: int = 32, dataset: Optional[Dataset] = None
    ) -> Iterator[Dict[str, torch.Tensor]]:
        """
        Create a streaming iterator for large datasets.

        Args:
            batch_size: Batch size for iteration
            dataset: Dataset to iterate over

        Yields:
            Batches of tokenized data
        """
        if dataset is None:
            if self._processed_dataset is None:
                raise ValueError(
                    "No processed dataset available. Call preprocess_dataset() first."
                )
            dataset = self._processed_dataset

        dataloader = self.get_dataloader(
            batch_size=batch_size, shuffle=False, num_workers=0, dataset=dataset
        )

        for batch in dataloader:
            yield batch

    def get_dataset_info(self) -> Dict[str, Any]:
        """
        Get information about the loaded dataset.

        Returns:
            Dictionary containing dataset metadata
        """
        info = {
            "dataset_name": self.config.dataset_name,
            "dataset_split": self.config.dataset_split,
            "text_column": self.config.text_column,
            "chunk_size": self.config.chunk_size,
            "streaming": self.config.streaming,
        }

        if self._dataset is not None:
            info.update(
                {
                    "raw_size": len(self._dataset),
                    "raw_columns": self._dataset.column_names,
                }
            )

        if self._processed_dataset is not None:
            info.update(
                {
                    "processed_size": len(self._processed_dataset),
                    "processed_columns": self._processed_dataset.column_names,
                }
            )

        return info

    @property
    def dataset(self) -> Optional[Dataset]:
        """Get the raw dataset."""
        return self._dataset

    @property
    def processed_dataset(self) -> Optional[Dataset]:
        """Get the processed dataset."""
        return self._processed_dataset


class MemoryMappedDataset(TorchDataset):
    """
    Memory-mapped dataset for very large datasets.

    Useful for datasets that don't fit in memory.
    """

    def __init__(
        self,
        data_path: Union[str, Path],
        sequence_length: int,
        max_examples: Optional[int] = None,
        dtype: np.dtype = np.uint16,
    ):
        """
        Initialize memory-mapped dataset.

        Args:
            data_path: Path to memory-mapped data file
            sequence_length: Length of each sequence
            max_examples: Maximum number of examples to use
            dtype: Data type of the stored tokens
        """
        self.data_path = Path(data_path)
        self.sequence_length = sequence_length
        self.dtype = dtype

        # Load memory-mapped array
        self.mmap = np.memmap(self.data_path, dtype=dtype, mode="r").reshape(
            -1, sequence_length
        )

        if max_examples is not None:
            self.mmap = self.mmap[:max_examples]

    def __len__(self) -> int:
        """Get dataset length."""
        return len(self.mmap)

    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """Get item by index."""
        return {"input_ids": torch.from_numpy(self.mmap[idx].astype(np.int64))}
